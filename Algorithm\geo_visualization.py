import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from geo_analysis import generate_geo_data, create_interactive_map, create_heatmap, spatial_analysis

def main():
    st.set_page_config(
        page_title="地理信息数据分析",
        page_icon="🗺️",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # 自定义CSS样式
    st.markdown("""
    <style>
    .main {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 20px;
        border-radius: 15px;
        color: #000;
    }

    .stApp {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .title {
        color: #2c3e50;
        font-size: 36px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }

    .subtitle {
        color: #34495e;
        font-size: 18px;
        text-align: center;
        margin-bottom: 30px;
        font-weight: 300;
    }

    .metric-container {
        background: white;
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        margin: 10px 0;
        text-align: center;
    }

    .metric-value {
        font-size: 28px;
        font-weight: bold;
        color: #3498db;
        margin-bottom: 5px;
    }

    .metric-label {
        font-size: 14px;
        color: #7f8c8d;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .section-header {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        padding: 15px;
        border-radius: 10px;
        text-align: center;
        font-size: 20px;
        font-weight: bold;
        margin: 20px 0 10px 0;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    }

    .info-box {
        background: #e8f4fd;
        border-left: 4px solid #3498db;
        padding: 15px;
        margin: 15px 0;
        border-radius: 5px;
    }

    .warning-box {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 15px;
        margin: 15px 0;
        border-radius: 5px;
    }

    .success-box {
        background: #d4edda;
        border-left: 4px solid #28a745;
        padding: 15px;
        margin: 15px 0;
        border-radius: 5px;
    }

    /* 确保所有文字都是黑色 */
    .stSelectbox label, .stRadio label, .stCheckbox label {
        color: #000000 !important;
        font-weight: 600;
    }

    /* 侧边栏样式 */
    .css-1d391kg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .css-1d391kg .stSelectbox label {
        color: white !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # 页面标题
    st.markdown('<div class="title">🗺️ 地理信息数据分析系统</div>', unsafe_allow_html=True)
    st.markdown('<div class="subtitle">污染源与监测站空间分布可视化分析</div>', unsafe_allow_html=True)

    # 侧边栏控制面板
    st.sidebar.markdown("## 🎛️ 控制面板")

    # 加载数据
    with st.spinner("正在加载地理数据..."):
        stations_df, sources_df = generate_geo_data()

    # 侧边栏选项
    analysis_type = st.sidebar.selectbox(
        "选择分析类型",
        ["交互式地图", "污染物热力图", "空间统计分析", "污染源分析"]
    )

    pollutant_options = ['PM2.5', 'PM10', 'SO2', 'NO2', 'CO', 'O3']
    selected_pollutant = st.sidebar.selectbox(
        "选择污染物",
        pollutant_options,
        index=0
    )

    # 数据过滤选项
    st.sidebar.markdown("### 📊 数据过滤")

    # AQI等级过滤
    aqi_levels = st.sidebar.multiselect(
        "选择AQI等级",
        options=stations_df['AQI_level'].unique(),
        default=stations_df['AQI_level'].unique()
    )

    # 污染源类型过滤
    source_types = st.sidebar.multiselect(
        "选择污染源类型",
        options=sources_df['source_type'].unique(),
        default=sources_df['source_type'].unique()
    )

    # 过滤数据
    filtered_stations = stations_df[stations_df['AQI_level'].isin(aqi_levels)]
    filtered_sources = sources_df[sources_df['source_type'].isin(source_types)]

    # 显示数据概览
    st.markdown('<div class="section-header">📈 数据概览</div>', unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="metric-container">
            <div class="metric-value">{len(filtered_stations)}</div>
            <div class="metric-label">监测站数量</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="metric-container">
            <div class="metric-value">{len(filtered_sources)}</div>
            <div class="metric-label">污染源数量</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        avg_pm25 = filtered_stations['PM2.5'].mean()
        st.markdown(f"""
        <div class="metric-container">
            <div class="metric-value">{avg_pm25:.1f}</div>
            <div class="metric-label">平均PM2.5浓度</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        good_stations = len(filtered_stations[filtered_stations['AQI_level'].isin(['优', '良'])])
        good_ratio = (good_stations / len(filtered_stations) * 100) if len(filtered_stations) > 0 else 0
        st.markdown(f"""
        <div class="metric-container">
            <div class="metric-value">{good_ratio:.1f}%</div>
            <div class="metric-label">优良率</div>
        </div>
        """, unsafe_allow_html=True)

    # 根据选择的分析类型显示不同内容
    if analysis_type == "交互式地图":
        st.markdown('<div class="section-header">🗺️ 交互式地图</div>', unsafe_allow_html=True)

        st.markdown("""
        <div class="info-box">
        <strong>使用说明：</strong><br>
        • 🔵 圆形标记代表监测站，颜色表示空气质量等级<br>
        • 🏭 工厂图标代表污染源，颜色表示排放等级<br>
        • 点击标记可查看详细信息<br>
        • 使用鼠标滚轮可缩放地图
        </div>
        """, unsafe_allow_html=True)

        # 创建并显示交互式地图
        map_fig = create_interactive_map(filtered_stations, filtered_sources, selected_pollutant)
        st.plotly_chart(map_fig, use_container_width=True)

    elif analysis_type == "污染物热力图":
        st.markdown('<div class="section-header">🌡️ 污染物浓度热力图</div>', unsafe_allow_html=True)

        # 创建热力图
        heatmap_fig = create_heatmap(filtered_stations, selected_pollutant)
        st.plotly_chart(heatmap_fig, use_container_width=True)

        # 显示污染物统计信息
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### 📊 污染物统计")
            stats_df = filtered_stations[pollutant_options].describe()
            st.dataframe(stats_df.round(2))

        with col2:
            st.markdown("### 📈 浓度分布")
            fig_hist = px.histogram(
                filtered_stations,
                x=selected_pollutant,
                nbins=20,
                title=f"{selected_pollutant} 浓度分布直方图"
            )
            fig_hist.update_layout(height=400)
            st.plotly_chart(fig_hist, use_container_width=True)

    elif analysis_type == "空间统计分析":
        st.markdown('<div class="section-header">📊 空间统计分析</div>', unsafe_allow_html=True)

        # 进行空间分析
        stats = spatial_analysis(filtered_stations, filtered_sources)

        # 显示统计结果
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### 📈 基础统计")
            for key, value in stats.items():
                if isinstance(value, float):
                    st.metric(key, f"{value:.2f}")
                else:
                    st.metric(key, value)

        with col2:
            st.markdown("### 🎯 空气质量分布")
            aqi_counts = filtered_stations['AQI_level'].value_counts()
            fig_pie = px.pie(
                values=aqi_counts.values,
                names=aqi_counts.index,
                title="空气质量等级分布"
            )
            st.plotly_chart(fig_pie, use_container_width=True)

    else:  # 污染源分析
        st.markdown('<div class="section-header">🏭 污染源分析</div>', unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### 🏭 污染源类型分布")
            source_counts = filtered_sources['source_type'].value_counts()
            fig_bar = px.bar(
                x=source_counts.index,
                y=source_counts.values,
                title="污染源类型统计",
                labels={'x': '污染源类型', 'y': '数量'}
            )
            st.plotly_chart(fig_bar, use_container_width=True)

        with col2:
            st.markdown("### 📊 排放等级分布")
            emission_counts = filtered_sources['emission_level'].value_counts()
            fig_pie = px.pie(
                values=emission_counts.values,
                names=emission_counts.index,
                title="排放等级分布"
            )
            st.plotly_chart(fig_pie, use_container_width=True)

        # 显示污染源详细信息表
        st.markdown("### 📋 污染源详细信息")
        display_sources = filtered_sources[['name', 'source_type', 'emission_level', 'daily_emission', 'established_year']].copy()
        display_sources.columns = ['名称', '类型', '排放等级', '日排放量(吨)', '建立年份']
        st.dataframe(display_sources, use_container_width=True)

    # 页面底部信息
    st.markdown("---")
    st.markdown("""
    <div class="info-box">
    <strong>💡 系统说明：</strong><br>
    本系统提供污染源和监测站的地理位置可视化，支持多种空间分析功能。
    数据格式支持Shapefile、GeoJSON等地理信息标准格式，包含完整的地理坐标信息。
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
