import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import random
from datetime import datetime, timedelta

# 生成模拟地理数据
@st.cache_data
def generate_geo_data():
    """生成模拟的污染源和监测站地理数据"""

    # 北京市中心坐标范围
    center_lat, center_lon = 39.9042, 116.4074

    # 生成监测站数据
    monitoring_stations = []
    station_names = [
        "东城监测站", "西城监测站", "朝阳监测站", "海淀监测站", "丰台监测站",
        "石景山监测站", "门头沟监测站", "房山监测站", "通州监测站", "顺义监测站",
        "昌平监测站", "大兴监测站", "怀柔监测站", "平谷监测站", "密云监测站", "延庆监测站"
    ]

    for i, name in enumerate(station_names):
        # 在北京市范围内随机生成坐标
        lat = center_lat + random.uniform(-0.5, 0.5)
        lon = center_lon + random.uniform(-0.8, 0.8)

        # 生成当前污染数据
        pm25 = random.uniform(20, 150)
        pm10 = pm25 * random.uniform(1.2, 2.0)
        so2 = random.uniform(5, 50)
        no2 = random.uniform(15, 80)
        co = random.uniform(0.5, 3.0)
        o3 = random.uniform(30, 120)

        # 确定AQI等级
        if pm25 <= 35:
            aqi_level = "优"
            aqi_color = "#00e400"
        elif pm25 <= 75:
            aqi_level = "良"
            aqi_color = "#ffff00"
        elif pm25 <= 115:
            aqi_level = "轻度污染"
            aqi_color = "#ff7e00"
        elif pm25 <= 150:
            aqi_level = "中度污染"
            aqi_color = "#ff0000"
        else:
            aqi_level = "重度污染"
            aqi_color = "#8f3f97"

        monitoring_stations.append({
            'id': f'MS_{i+1:03d}',
            'name': name,
            'latitude': lat,
            'longitude': lon,
            'type': 'monitoring_station',
            'PM2.5': round(pm25, 1),
            'PM10': round(pm10, 1),
            'SO2': round(so2, 1),
            'NO2': round(no2, 1),
            'CO': round(co, 2),
            'O3': round(o3, 1),
            'AQI_level': aqi_level,
            'AQI_color': aqi_color,
            'last_update': datetime.now() - timedelta(minutes=random.randint(1, 30))
        })

    # 生成污染源数据
    pollution_sources = []
    source_types = ['工厂', '发电厂', '化工厂', '钢铁厂', '水泥厂', '炼油厂']

    for i in range(25):
        lat = center_lat + random.uniform(-0.6, 0.6)
        lon = center_lon + random.uniform(-0.9, 0.9)

        source_type = random.choice(source_types)
        emission_level = random.choice(['低', '中', '高'])

        # 根据排放等级设置颜色
        if emission_level == '低':
            color = '#90EE90'  # 浅绿色
            size = 8
        elif emission_level == '中':
            color = '#FFA500'  # 橙色
            size = 12
        else:
            color = '#FF4500'  # 红色
            size = 16

        pollution_sources.append({
            'id': f'PS_{i+1:03d}',
            'name': f'{source_type}_{i+1}',
            'latitude': lat,
            'longitude': lon,
            'type': 'pollution_source',
            'source_type': source_type,
            'emission_level': emission_level,
            'color': color,
            'size': size,
            'daily_emission': random.uniform(10, 500),  # 吨/天
            'established_year': random.randint(1980, 2020)
        })

    return pd.DataFrame(monitoring_stations), pd.DataFrame(pollution_sources)

# 创建交互式地图
def create_interactive_map(stations_df, sources_df, selected_pollutant='PM2.5'):
    """创建包含监测站和污染源的交互式地图"""

    # 创建监测站散点图
    fig = go.Figure()

    # 添加监测站数据
    for idx, station in stations_df.iterrows():
        # 根据选择的污染物确定颜色
        if selected_pollutant == 'PM2.5':
            value = station['PM2.5']
            if value <= 35:
                color = 'green'
            elif value <= 75:
                color = 'yellow'
            elif value <= 115:
                color = 'orange'
            else:
                color = 'red'
        else:
            color = station['AQI_color']

        # 创建悬停信息
        hover_text = f"""
        <b>{station['name']}</b><br>
        PM2.5: {station['PM2.5']} μg/m³<br>
        PM10: {station['PM10']} μg/m³<br>
        SO2: {station['SO2']} μg/m³<br>
        NO2: {station['NO2']} μg/m³<br>
        CO: {station['CO']} mg/m³<br>
        O3: {station['O3']} μg/m³<br>
        空气质量: {station['AQI_level']}<br>
        更新时间: {station['last_update'].strftime('%H:%M')}
        """

        fig.add_trace(go.Scattermapbox(
            lat=[station['latitude']],
            lon=[station['longitude']],
            mode='markers',
            marker=dict(
                size=15,
                color=color,
                opacity=0.8
            ),
            text=hover_text,
            hoverinfo='text',
            name=f"监测站-{station['AQI_level']}",
            showlegend=False
        ))

    # 添加污染源数据
    for idx, source in sources_df.iterrows():
        # 根据排放等级确定颜色和大小
        if source['emission_level'] == '低':
            color = 'lightgreen'
            size = 10
        elif source['emission_level'] == '中':
            color = 'orange'
            size = 15
        else:
            color = 'red'
            size = 20

        hover_text = f"""
        <b>{source['name']}</b><br>
        类型: {source['source_type']}<br>
        排放等级: {source['emission_level']}<br>
        日排放量: {source['daily_emission']:.1f} 吨<br>
        建立年份: {source['established_year']}
        """

        fig.add_trace(go.Scattermapbox(
            lat=[source['latitude']],
            lon=[source['longitude']],
            mode='markers',
            marker=dict(
                size=size,
                color=color,
                symbol='square',
                opacity=0.8
            ),
            text=hover_text,
            hoverinfo='text',
            name=f"污染源-{source['emission_level']}排放",
            showlegend=False
        ))

    # 设置地图布局
    fig.update_layout(
        mapbox=dict(
            style="open-street-map",
            center=dict(lat=39.9042, lon=116.4074),
            zoom=9
        ),
        height=600,
        margin=dict(l=0, r=0, t=50, b=0),
        title=f"污染源与监测站分布图 - {selected_pollutant}"
    )

    return fig

# 创建污染物分布热力图
def create_heatmap(stations_df, pollutant='PM2.5'):
    """创建污染物浓度分布热力图"""

    fig = px.scatter_mapbox(
        stations_df,
        lat='latitude',
        lon='longitude',
        color=pollutant,
        size=pollutant,
        hover_name='name',
        hover_data={
            'PM2.5': True,
            'PM10': True,
            'AQI_level': True,
            'latitude': False,
            'longitude': False
        },
        color_continuous_scale='Viridis',
        size_max=20,
        zoom=9,
        center={'lat': 39.9042, 'lon': 116.4074},
        mapbox_style='open-street-map',
        title=f'{pollutant} 浓度分布热力图'
    )

    fig.update_layout(
        height=600,
        margin=dict(l=0, r=0, t=50, b=0)
    )

    return fig

# 空间分析统计
def spatial_analysis(stations_df, sources_df):
    """进行空间分析统计"""

    # 计算基本统计信息
    stats = {
        '监测站总数': len(stations_df),
        '污染源总数': len(sources_df),
        '平均PM2.5浓度': stations_df['PM2.5'].mean(),
        '最高PM2.5浓度': stations_df['PM2.5'].max(),
        '最低PM2.5浓度': stations_df['PM2.5'].min(),
        '空气质量优良站点数': len(stations_df[stations_df['AQI_level'].isin(['优', '良'])]),
        '污染站点数': len(stations_df[~stations_df['AQI_level'].isin(['优', '良'])])
    }

    return stats
