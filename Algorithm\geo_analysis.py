import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import random
from datetime import datetime, timedelta

# 生成模拟地理数据
@st.cache_data
def generate_geo_data():
    """生成模拟的污染源和监测站地理数据"""

    # 北京市中心坐标范围
    center_lat, center_lon = 39.9042, 116.4074

    # 生成监测站数据
    monitoring_stations = []
    station_names = [
        "东城监测站", "西城监测站", "朝阳监测站", "海淀监测站", "丰台监测站",
        "石景山监测站", "门头沟监测站", "房山监测站", "通州监测站", "顺义监测站",
        "昌平监测站", "大兴监测站", "怀柔监测站", "平谷监测站", "密云监测站", "延庆监测站"
    ]

    for i, name in enumerate(station_names):
        # 在北京市范围内随机生成坐标
        lat = center_lat + random.uniform(-0.5, 0.5)
        lon = center_lon + random.uniform(-0.8, 0.8)

        # 生成当前污染数据
        pm25 = random.uniform(20, 150)
        pm10 = pm25 * random.uniform(1.2, 2.0)
        so2 = random.uniform(5, 50)
        no2 = random.uniform(15, 80)
        co = random.uniform(0.5, 3.0)
        o3 = random.uniform(30, 120)

        # 确定AQI等级
        if pm25 <= 35:
            aqi_level = "优"
            aqi_color = "#00e400"
        elif pm25 <= 75:
            aqi_level = "良"
            aqi_color = "#ffff00"
        elif pm25 <= 115:
            aqi_level = "轻度污染"
            aqi_color = "#ff7e00"
        elif pm25 <= 150:
            aqi_level = "中度污染"
            aqi_color = "#ff0000"
        else:
            aqi_level = "重度污染"
            aqi_color = "#8f3f97"

        monitoring_stations.append({
            'id': f'MS_{i+1:03d}',
            'name': name,
            'latitude': lat,
            'longitude': lon,
            'type': 'monitoring_station',
            'PM2.5': round(pm25, 1),
            'PM10': round(pm10, 1),
            'SO2': round(so2, 1),
            'NO2': round(no2, 1),
            'CO': round(co, 2),
            'O3': round(o3, 1),
            'AQI_level': aqi_level,
            'AQI_color': aqi_color,
            'last_update': datetime.now() - timedelta(minutes=random.randint(1, 30))
        })

    # 生成污染源数据
    pollution_sources = []
    source_types = ['工厂', '发电厂', '化工厂', '钢铁厂', '水泥厂', '炼油厂']

    for i in range(25):
        lat = center_lat + random.uniform(-0.6, 0.6)
        lon = center_lon + random.uniform(-0.9, 0.9)

        source_type = random.choice(source_types)
        emission_level = random.choice(['低', '中', '高'])

        # 根据排放等级设置颜色
        if emission_level == '低':
            color = '#90EE90'  # 浅绿色
            size = 8
        elif emission_level == '中':
            color = '#FFA500'  # 橙色
            size = 12
        else:
            color = '#FF4500'  # 红色
            size = 16

        pollution_sources.append({
            'id': f'PS_{i+1:03d}',
            'name': f'{source_type}_{i+1}',
            'latitude': lat,
            'longitude': lon,
            'type': 'pollution_source',
            'source_type': source_type,
            'emission_level': emission_level,
            'color': color,
            'size': size,
            'daily_emission': random.uniform(10, 500),  # 吨/天
            'established_year': random.randint(1980, 2020)
        })

    return pd.DataFrame(monitoring_stations), pd.DataFrame(pollution_sources)

# 创建交互式地图（性能优化版本）
def create_interactive_map(stations_df, sources_df, selected_pollutant='PM2.5'):
    """创建包含监测站和污染源的交互式地图（性能优化版本）"""

    fig = go.Figure()

    # 性能优化：批量处理监测站数据
    if not stations_df.empty:
        # 准备监测站数据
        station_colors = []
        station_hover_texts = []

        for idx, station in stations_df.iterrows():
            # 根据选择的污染物确定颜色
            if selected_pollutant == 'PM2.5':
                value = station['PM2.5']
                if value <= 35:
                    color = 'green'
                elif value <= 75:
                    color = 'yellow'
                elif value <= 115:
                    color = 'orange'
                else:
                    color = 'red'
            else:
                color = station['AQI_color']

            station_colors.append(color)

            # 简化悬停信息以提高性能
            hover_text = f"{station['name']}<br>PM2.5: {station['PM2.5']}<br>质量: {station['AQI_level']}"
            station_hover_texts.append(hover_text)

        # 一次性添加所有监测站
        fig.add_trace(go.Scattermapbox(
            lat=stations_df['latitude'].tolist(),
            lon=stations_df['longitude'].tolist(),
            mode='markers',
            marker=dict(
                size=12,
                color=station_colors,
                opacity=0.8,
                line=dict(width=1, color='white')
            ),
            text=station_hover_texts,
            hoverinfo='text',
            name="监测站",
            showlegend=True
        ))

    # 性能优化：批量处理污染源数据
    if not sources_df.empty:
        # 按排放等级分组处理
        for emission_level in sources_df['emission_level'].unique():
            level_sources = sources_df[sources_df['emission_level'] == emission_level]

            # 根据排放等级设置颜色和大小
            if emission_level == '低':
                color = 'lightgreen'
                size = 8
            elif emission_level == '中':
                color = 'orange'
                size = 12
            else:
                color = 'red'
                size = 16

            # 简化悬停信息
            hover_texts = [f"{row['name']}<br>类型: {row['source_type']}<br>等级: {row['emission_level']}"
                          for _, row in level_sources.iterrows()]

            fig.add_trace(go.Scattermapbox(
                lat=level_sources['latitude'].tolist(),
                lon=level_sources['longitude'].tolist(),
                mode='markers',
                marker=dict(
                    size=size,
                    color=color,
                    symbol='square',
                    opacity=0.7,
                    line=dict(width=1, color='white')
                ),
                text=hover_texts,
                hoverinfo='text',
                name=f"污染源-{emission_level}排放",
                showlegend=True
            ))

    # 优化地图布局
    fig.update_layout(
        mapbox=dict(
            style="open-street-map",
            center=dict(lat=39.9042, lon=116.4074),
            zoom=9
        ),
        height=600,
        margin=dict(l=0, r=0, t=30, b=0),
        title=dict(
            text=f"污染源与监测站分布图 - {selected_pollutant}",
            x=0.5,
            font=dict(size=16)
        ),
        legend=dict(
            yanchor="top",
            y=0.99,
            xanchor="left",
            x=0.01,
            bgcolor="rgba(255,255,255,0.8)"
        )
    )

    return fig

# 创建轻量级地图（低性能设备专用）
def create_lightweight_map(stations_df, sources_df, selected_pollutant='PM2.5'):
    """创建轻量级地图，适用于低性能设备"""

    # 数据采样：只显示部分数据点
    max_stations = 10
    max_sources = 15

    if len(stations_df) > max_stations:
        stations_sample = stations_df.sample(n=max_stations)
    else:
        stations_sample = stations_df

    if len(sources_df) > max_sources:
        sources_sample = sources_df.sample(n=max_sources)
    else:
        sources_sample = sources_df

    fig = px.scatter_mapbox(
        stations_sample,
        lat='latitude',
        lon='longitude',
        color='AQI_level',
        hover_name='name',
        hover_data={'latitude': False, 'longitude': False, 'PM2.5': True},
        color_discrete_map={
            '优': 'green',
            '良': 'yellow',
            '轻度污染': 'orange',
            '中度污染': 'red',
            '重度污染': 'purple'
        },
        zoom=9,
        center={'lat': 39.9042, 'lon': 116.4074},
        mapbox_style='open-street-map',
        title=f'简化地图 - {selected_pollutant} (显示 {len(stations_sample)} 个监测站)'
    )

    fig.update_layout(
        height=500,
        margin=dict(l=0, r=0, t=50, b=0)
    )

    return fig

# 创建污染物分布热力图
def create_heatmap(stations_df, pollutant='PM2.5'):
    """创建污染物浓度分布热力图"""

    fig = px.scatter_mapbox(
        stations_df,
        lat='latitude',
        lon='longitude',
        color=pollutant,
        size=pollutant,
        hover_name='name',
        hover_data={
            'PM2.5': True,
            'PM10': True,
            'AQI_level': True,
            'latitude': False,
            'longitude': False
        },
        color_continuous_scale='Viridis',
        size_max=20,
        zoom=9,
        center={'lat': 39.9042, 'lon': 116.4074},
        mapbox_style='open-street-map',
        title=f'{pollutant} 浓度分布热力图'
    )

    fig.update_layout(
        height=600,
        margin=dict(l=0, r=0, t=50, b=0)
    )

    return fig

# 空间分析统计
def spatial_analysis(stations_df, sources_df):
    """进行空间分析统计"""

    # 计算基本统计信息
    stats = {
        '监测站总数': len(stations_df),
        '污染源总数': len(sources_df),
        '平均PM2.5浓度': stations_df['PM2.5'].mean(),
        '最高PM2.5浓度': stations_df['PM2.5'].max(),
        '最低PM2.5浓度': stations_df['PM2.5'].min(),
        '空气质量优良站点数': len(stations_df[stations_df['AQI_level'].isin(['优', '良'])]),
        '污染站点数': len(stations_df[~stations_df['AQI_level'].isin(['优', '良'])])
    }

    return stats
